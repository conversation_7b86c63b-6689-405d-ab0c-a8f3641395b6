use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, Serialize, Deserialize)]
pub struct CleanupItem {
    pub id: String,
    pub name: String,
    pub path: String,
    pub size: u64,
    pub item_type: String,
    pub selected: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScanResult {
    pub items: Vec<CleanupItem>,
    pub total_size: u64,
    pub scan_time: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CleanupProgress {
    pub current: u32,
    pub total: u32,
    pub current_item: String,
    pub is_complete: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CleanupResult {
    pub cleaned_items: Vec<CleanupItem>,
    pub total_freed_space: u64,
    pub errors: Vec<String>,
}

// Placeholder functions - will be implemented in later tasks
pub fn scan_system() -> Result<ScanResult, String> {
    // Implementation will be added in task 2
    Ok(ScanResult {
        items: vec![],
        total_size: 0,
        scan_time: 0,
    })
}

pub fn cleanup_items(items: Vec<CleanupItem>) -> Result<CleanupResult, String> {
    // Implementation will be added in task 3
    Ok(CleanupResult {
        cleaned_items: vec![],
        total_freed_space: 0,
        errors: vec![],
    })
}