mod cleanup;

use cleanup::{<PERSON>an<PERSON><PERSON>ult, CleanupResult, CleanupItem};

#[tauri::command]
async fn scan_system() -> Result<ScanResult, String> {
    cleanup::scan_system()
}

#[tauri::command]
async fn cleanup_items(items: Vec<CleanupItem>) -> Result<CleanupResult, String> {
    cleanup::cleanup_items(items)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![scan_system, cleanup_items])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
