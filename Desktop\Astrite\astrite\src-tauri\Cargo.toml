# REMINDER: Always use Context7 MCP to check for latest Tauri and Rust crate versions!
# Last updated: July 2025 - All dependencies updated to latest stable versions

[package]
name = "astrite"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "astrite_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.6", features = [] }

[dependencies]
tauri = { version = "2.6", features = ["fs-read-dir", "fs-read-file", "fs-remove-dir", "fs-remove-file", "path-all"] }
tauri-plugin-opener = "2.6"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
rayon = "1.8"
rusqlite = { version = "0.32", features = ["bundled"] }
xxhash-rust = { version = "0.8", features = ["xxh3"] }
image = "0.25"
ollama-rs = "0.2"

