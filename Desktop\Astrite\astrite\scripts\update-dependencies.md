# Dependency Update Script

## Using Context7 to Check Latest Versions

### Step 1: Check React Ecosystem
```javascript
// In Kiro with Context7 MCP enabled:
mcp_Context7_resolve_library_id("react")
mcp_Context7_get_library_docs("/context7/react_dev", "latest version")
```

### Step 2: Check Vite
```javascript
mcp_Context7_resolve_library_id("vite") 
mcp_Context7_get_library_docs("/vitejs/vite", "latest version")
```

### Step 3: Check Tauri
```javascript
mcp_Context7_resolve_library_id("tauri")
mcp_Context7_get_library_docs("/context7/rs-tauri", "latest version")
```

### Step 4: Check Other Dependencies
```javascript
// TypeScript
mcp_Context7_resolve_library_id("typescript")

// Tailwind CSS
mcp_Context7_resolve_library_id("tailwindcss")

// Radix UI components
mcp_Context7_resolve_library_id("@radix-ui/react-dialog")
```

## Update Commands

### Frontend Dependencies
```bash
npm update
# or for major version updates:
npm install react@latest react-dom@latest
npm install --save-dev @types/react@latest @types/react-dom@latest
npm install --save-dev vite@latest typescript@latest
```

### Backend Dependencies (Rust)
```bash
cd src-tauri
cargo update
# Check for major version updates in Cargo.toml manually
```

## Testing After Updates
1. `npm run build` - Ensure build works
2. `npm run tauri dev` - Test development mode
3. `npm run tauri build` - Test production build
4. Test all major functionality

## Breaking Changes Checklist
- [ ] React: Check for new hooks, Context API changes
- [ ] Vite: Check build target changes, Node.js requirements  
- [ ] Tauri: Check API changes, new features
- [ ] TypeScript: Check for new strict checks
- [ ] Update DEVELOPMENT_NOTES.md with new versions