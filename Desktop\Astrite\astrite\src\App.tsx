import { useState } from "react";
import { SystemScan } from "./components/SystemScan";
import { CleanupProgress } from "./components/CleanupProgress";
import { CleanupResults } from "./components/CleanupResults";
import "./App.css";

type AppState = 'scan' | 'progress' | 'results';

function App() {
  const [currentState, setCurrentState] = useState<AppState>('scan');

  const renderCurrentView = () => {
    switch (currentState) {
      case 'scan':
        return <SystemScan onScanComplete={() => setCurrentState('progress')} />;
      case 'progress':
        return <CleanupProgress onCleanupComplete={() => setCurrentState('results')} />;
      case 'results':
        return <CleanupResults onReset={() => setCurrentState('scan')} />;
      default:
        return <SystemScan onScanComplete={() => setCurrentState('progress')} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          Windows System Cleaner
        </h1>
        {renderCurrentView()}
      </div>
    </div>
  );
}

export default App;