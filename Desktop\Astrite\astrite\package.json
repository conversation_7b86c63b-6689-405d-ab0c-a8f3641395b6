{"name": "astrite", "private": true, "version": "0.1.0", "type": "module", "_comment": "ALWAYS use Context7 MCP to check for latest versions before updating dependencies!", "engines": {"node": ">=20.19.0", "npm": ">=10.0.0"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-opener": "^2", "@types/node": "^24.0.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.7.2", "vite": "^7.0.0"}}