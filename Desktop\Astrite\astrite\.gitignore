# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Tauri
src-tauri/target
src-tauri/Cargo.lock

# OS
Thumbs.db
.DS_Store

# IDE
.vscode/
.idea/

# Build outputs
/dist/
/build/

# Dependencies
node_modules/
target/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Rust
**/*.rs.bk
Cargo.lock

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini