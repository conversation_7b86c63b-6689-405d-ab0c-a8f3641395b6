name: Dependency Update Check

on:
  schedule:
    # Run monthly on the 1st at 9 AM UTC
    - cron: '0 9 1 * *'
  workflow_dispatch: # Allow manual trigger

jobs:
  check-dependencies:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.12.0'
          cache: 'npm'

      - name: Check for outdated packages
        run: |
          npm outdated || true
          echo "🚨 REMINDER: Use Context7 MCP to check for latest versions!"
          echo "📝 Update DEVELOPMENT_NOTES.md after updating dependencies"
          echo "🔄 Follow scripts/update-dependencies.md for proper update process"

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Check Rust dependencies
        working-directory: src-tauri
        run: |
          cargo outdated || echo "Install cargo-outdated: cargo install cargo-outdated"
          echo "🦀 Check Tauri versions using Context7 MCP!"

      - name: Create Issue if outdated
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🚨 Dependencies Need Updating',
              body: `
              ## Outdated Dependencies Detected
              
              **Action Required:**
              1. Use Context7 MCP to check latest versions
              2. Follow \`scripts/update-dependencies.md\`
              3. Update \`DEVELOPMENT_NOTES.md\` with new versions
              4. Test thoroughly after updates
              
              **Remember:** Always use latest stable versions!
              `
            })