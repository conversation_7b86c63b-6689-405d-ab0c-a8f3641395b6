# Design Document

## Overview

Astrite is a desktop application built with Rust + Tauri + React that provides AI-powered file organization and duplicate detection. The application runs entirely locally, using Ollama with Mistral for file categorization and advanced hashing algorithms for duplicate detection. The design prioritizes performance, privacy, and user experience with a clean, modern interface.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[React Frontend] --> <PERSON><PERSON>[Tauri Bridge]
    Tauri --> Core[Rust Core Engine]
    Core --> AI[AI Classifier]
    Core --> Dup[Duplicate Engine]
    Core --> Undo[Undo Manager]
    Core --> FS[File System]
    AI --> Ollama[Ollama + Mistral]
    Undo --> SQLite[(SQLite DB)]
    FS --> NTFS[Windows NTFS]
```

### Component Breakdown

**Frontend Layer (React + TypeScript)**
- Single-window Tauri application
- Drag-drop interface for folder input
- Preview/edit interface for file categorization
- Progress tracking and cancellation controls
- History view for undo operations

**Backend Layer (Rust)**
- File scanning and categorization engine
- Duplicate detection with parallel processing
- Undo system with SQLite persistence
- File system operations with error handling

**AI Layer (Local)**
- Ollama integration for Mistral model inference
- Fallback extension-based classification
- Category mapping and confidence scoring

## Components and Interfaces

### 1. File Classifier Component

**Purpose:** Categorize files using AI with fallback rules

**Interface:**
```rust
pub struct FileClassifier {
    ollama_client: OllamaClient,
    fallback_rules: ExtensionRules,
}

pub enum FileCategory {
    Docs, Images, Videos, Audio, Archives, Installers, Code, Misc
}

impl FileClassifier {
    pub async fn classify_file(&self, file_path: &Path) -> Result<FileCategory>;
    pub async fn classify_batch(&self, files: Vec<PathBuf>) -> Vec<ClassificationResult>;
}
```

**Implementation Details:**
- Uses Ollama Rust client to communicate with local Mistral model
- Analyzes file extension, MIME type, and file content when needed
- Maintains confidence scores for AI predictions
- Falls back to extension mapping if AI fails or confidence is low
- Supports batch processing for performance

### 2. Duplicate Detection Engine

**Purpose:** Find exact and perceptual duplicates across file system

**Interface:**
```rust
pub struct DuplicateEngine {
    hasher: MultiHasher,
    thumbnail_generator: ThumbnailGen,
}

pub struct DuplicateGroup {
    pub master_file: PathBuf,
    pub duplicates: Vec<PathBuf>,
    pub hash_type: HashType,
    pub thumbnails: Vec<Thumbnail>,
}

impl DuplicateEngine {
    pub async fn scan_for_duplicates(&self, paths: Vec<PathBuf>) -> Vec<DuplicateGroup>;
    pub fn generate_thumbnails(&self, group: &DuplicateGroup) -> Result<()>;
}
```

**Implementation Details:**
- Uses xxHash for exact byte-level duplicate detection
- Implements pHash (perceptual hashing) for images and videos
- Parallel processing with Rayon for performance
- Generates thumbnails for visual comparison
- Smart master file selection based on file metadata

### 3. Undo Manager

**Purpose:** Track and reverse all file operations

**Interface:**
```rust
pub struct UndoManager {
    db: SqliteConnection,
}

pub struct Operation {
    pub id: i64,
    pub timestamp: DateTime<Utc>,
    pub operation_type: OperationType,
    pub file_moves: Vec<FileMove>,
    pub reversible: bool,
}

impl UndoManager {
    pub fn log_operation(&mut self, op: Operation) -> Result<i64>;
    pub fn get_history(&self, limit: usize) -> Result<Vec<Operation>>;
    pub async fn undo_operation(&mut self, op_id: i64) -> Result<()>;
}
```

**Implementation Details:**
- SQLite database stored in `%LOCALAPPDATA%\Astrite\undo.db`
- Full-text search capabilities for operation history
- Automatic cleanup of operations older than 90 days
- Batch operation support for large file sets
- Integrity checks before undo execution

### 4. File Organization Engine

**Purpose:** Execute file movements based on categorization

**Interface:**
```rust
pub struct FileOrganizer {
    base_path: PathBuf,
    classifier: FileClassifier,
    undo_manager: UndoManager,
}

pub struct OrganizationPlan {
    pub moves: Vec<PlannedMove>,
    pub conflicts: Vec<FileConflict>,
    pub estimated_time: Duration,
}

impl FileOrganizer {
    pub async fn create_plan(&self, source_folder: PathBuf) -> Result<OrganizationPlan>;
    pub async fn execute_plan(&mut self, plan: OrganizationPlan) -> Result<OperationResult>;
}
```

## Data Models

### File Classification Data
```rust
pub struct ClassificationResult {
    pub file_path: PathBuf,
    pub category: FileCategory,
    pub confidence: f32,
    pub method: ClassificationMethod, // AI or Fallback
}

pub struct PlannedMove {
    pub source: PathBuf,
    pub destination: PathBuf,
    pub category: FileCategory,
    pub size: u64,
    pub user_approved: bool,
}
```

### Duplicate Detection Data
```rust
pub struct FileHash {
    pub path: PathBuf,
    pub xxhash: u64,
    pub phash: Option<u64>, // Only for images/videos
    pub size: u64,
    pub modified: SystemTime,
}

pub struct Thumbnail {
    pub path: PathBuf,
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
}
```

### Undo System Data
```rust
pub struct FileMove {
    pub original_path: PathBuf,
    pub new_path: PathBuf,
    pub file_hash: String,
    pub timestamp: DateTime<Utc>,
}

pub enum OperationType {
    FileOrganization,
    DuplicateRemoval,
    ManualMove,
}
```

## Error Handling

### Error Categories
1. **File System Errors**: Permission denied, file in use, disk full
2. **AI Model Errors**: Ollama unavailable, model loading failure
3. **Database Errors**: SQLite corruption, disk space issues
4. **User Input Errors**: Invalid paths, cancelled operations

### Error Recovery Strategy
- Graceful degradation when AI is unavailable (fallback to extension rules)
- Atomic operations with rollback capability
- User-friendly error messages with suggested actions
- Automatic retry for transient failures
- Progress preservation for long-running operations

### Error Handling Implementation
```rust
pub enum AstriteError {
    FileSystem(std::io::Error),
    Database(rusqlite::Error),
    AI(OllamaError),
    UserCancelled,
    InvalidInput(String),
}

impl From<std::io::Error> for AstriteError {
    fn from(err: std::io::Error) -> Self {
        AstriteError::FileSystem(err)
    }
}
```

## Testing Strategy

### Unit Testing
- File classification accuracy testing with diverse file sets
- Hash collision testing for duplicate detection
- Database operation integrity testing
- Error handling and recovery testing

### Integration Testing
- End-to-end file organization workflows
- Undo/redo operation chains
- Large file set performance testing
- AI model integration testing

### Performance Testing
- Benchmark file classification speed (target: 1000+ files accurately)
- Duplicate detection performance (target: <30s for 10k files)
- Memory usage profiling for large operations
- UI responsiveness during background operations

### Test Data Sets
- Mixed file collections (1000+ files, various types)
- Large image collections for perceptual hashing
- Duplicate-heavy datasets for accuracy testing
- Edge cases: corrupted files, unusual extensions, very long paths

### Automated Testing Pipeline
```rust
#[cfg(test)]
mod tests {
    #[tokio::test]
    async fn test_file_classification_accuracy() {
        // Test AI classification against known file sets
    }
    
    #[tokio::test]
    async fn test_duplicate_detection_precision() {
        // Verify zero false positives on exact matches
    }
    
    #[tokio::test]
    async fn test_undo_reliability() {
        // Test undo operations with large file sets
    }
}
```