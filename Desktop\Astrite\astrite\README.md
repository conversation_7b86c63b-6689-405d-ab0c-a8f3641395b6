# Astrite - Windows System Cleaner

A modern Windows system cleaner built with <PERSON><PERSON>, <PERSON><PERSON>, and Type<PERSON>.

## 🚨 IMPORTANT: Always Use Latest Versions

This project is committed to using the **latest stable versions** of all dependencies. Before making any changes:

1. **Use Context7 MCP** to check for the latest versions
2. **Never accept outdated dependencies** in production
3. **Update monthly** at minimum

See `DEVELOPMENT_NOTES.md` and `scripts/update-dependencies.md` for detailed update procedures.

## Current Tech Stack (Updated July 2025)

- **React**: 19.0.0 (latest stable)
- **React DOM**: 19.0.0 (latest stable) 
- **Vite**: 7.0.0 (latest stable)
- **TypeScript**: 5.7.2 (latest stable)
- **Tauri**: 2.6.2 (latest stable)
- **Node.js**: Requires 20.19+ or 22.12+

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [<PERSON><PERSON>](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
- [<PERSON><PERSON> IDE](https://kiro.ai) with Context7 MCP for dependency management

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run tauri dev

# Build for production
npm run tauri build
```

## Updating Dependencies

**ALWAYS use Context7 MCP to check for latest versions before updating!**

See `scripts/update-dependencies.md` for detailed instructions.
