# Development Notes

## 🚨 IMPORTANT REMINDERS

### Always Use Latest Versions
- **ALWAYS** check for and use the latest versions of dependencies
- **NEVER** accept outdated packages in production
- Update dependencies regularly, at minimum monthly

### Use Context7 for Version Checking
- Use the Context7 MCP server to check latest versions before updating
- Context7 provides accurate, up-to-date information about package versions
- Command: Use the Context7 resolve and get docs functions to check versions

### Current Tech Stack (Updated to Latest - July 2025)
- **React**: 19.0.0 (latest stable)
- **React DOM**: 19.0.0 (latest stable)
- **Vite**: 7.0.0 (latest stable)
- **TypeScript**: 5.7.2 (latest stable)
- **Tauri**: 2.6.2 (latest stable)
- **Node.js**: Requires 20.19+ or 22.12+ (Vite 7 requirement)

### Breaking Changes Handled
- ✅ React 19: Updated createRoot usage, component prop types
- ✅ Vite 7: Updated build targets, Node.js requirements
- ✅ Tauri 2.6: Updated to latest stable version
- ✅ TypeScript 5.7: Updated type definitions

### Update Process
1. Use Context7 to check latest versions
2. Update package.json and Cargo.toml
3. Test build and functionality
4. Update this file with new versions
5. Commit changes

### Context7 Usage Examples
```bash
# Check React versions
mcp_Context7_resolve_library_id("react")
mcp_Context7_get_library_docs("/context7/react_dev", "latest version")

# Check Vite versions  
mcp_Context7_resolve_library_id("vite")
mcp_Context7_get_library_docs("/vitejs/vite", "latest version")

# Check Tauri versions
mcp_Context7_resolve_library_id("tauri")
mcp_Context7_get_library_docs("/context7/rs-tauri", "latest version")
```

### Next Update Check: August 2025
- Set calendar reminder to check for updates monthly
- Always use Context7 for accurate version information
- Never skip major version updates without proper testing

---
**Last Updated**: July 2025  
**Next Review**: August 2025