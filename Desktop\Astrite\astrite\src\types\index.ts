// Core type definitions for the system cleaner
export interface CleanupItem {
  id: string;
  name: string;
  path: string;
  size: number;
  type: 'temp' | 'cache' | 'log' | 'recycle';
  selected: boolean;
}

export interface ScanResult {
  items: CleanupItem[];
  totalSize: number;
  scanTime: number;
}

export interface CleanupProgress {
  current: number;
  total: number;
  currentItem: string;
  isComplete: boolean;
}

export interface CleanupResult {
  cleanedItems: CleanupItem[];
  totalFreedSpace: number;
  errors: string[];
}